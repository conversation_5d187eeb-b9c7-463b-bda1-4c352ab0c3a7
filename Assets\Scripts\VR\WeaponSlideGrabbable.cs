﻿using UnityEngine;

public class WeaponSlideGrabbable : Grabbable
{

    [HideInInspector]
    public float ungrabTime = -1;

    public bool IsGrabbed => currentGrabber != null;
    public override Vector3 Velocity => Vector3.zero;

    public override Vector3 AngularVelocity => Vector3.zero;

    public bool CanGrab;

    [SerializeField] private float minZSlideMovement = -0.1f; // Limit pull-back distance
    [SerializeField] private float maxZSlideMovement = 0.1f;  // Limit push distance


    private Vector3 initialLocalPosition;
    private Quaternion initialLocalRotation;
    private float initialLocalY;  // Store the local Y at grab start

    private void Awake()
    {
        networkGrabbable = GetComponent<NetworkGrabbable>();
    }

    public override void Ungrab()
    {
        StopVibration();

        base.Ungrab();

        if (networkGrabbable)
        {
            // When networked, we need to store ungrab info detailled position for some extrapolation of edge cases
            ungrabTime = Time.time;
        }
        DidUngrab();
    }
   
    private void Update()
    {
        // We handle the following if we are not online (in that case, the Follow will be called by the NetworkGrabbable during FUN and Render)
        if (networkGrabbable == null || networkGrabbable.Object == null)
        {
            if (IsGrabbed) Follow(followingTransform: transform, followedTransform: currentGrabber.transform);
        }

    }

    public override bool Grab(Grabber newGrabber)
    {
        if (!CanGrab)
            return false;

        if (base.Grab(newGrabber))
        {
            DidGrab();
            return true;
        }
        return false;
    }
    float initialLocalZ;
    public void Follow(Transform followingTransform, Transform followedTransform)
    {
        // Get the local forward (Z) direction of the parent (weapon)
        Vector3 localZAxis = followingTransform.parent.forward;

        // Get the hand's position in world space
        Vector3 handWorldPosition = followedTransform.position;

        // Project the hand's movement onto the local Z-axis of the parent
        Vector3 parentPosition = followingTransform.parent.position;
        float projectedZ = Vector3.Dot(handWorldPosition - parentPosition, localZAxis);

        // Clamp movement within allowed range
        projectedZ = Mathf.Clamp(projectedZ, minZSlideMovement, maxZSlideMovement);

        // Convert back to local space
        Vector3 localPos = followingTransform.parent.InverseTransformPoint(parentPosition + (localZAxis * projectedZ));

        // Keep X & Y fixed, only modify Z
        followingTransform.localPosition = new Vector3(localPos.x, initialLocalY, localPos.z);

        // Ensure it matches parent's rotation
        followingTransform.rotation = followingTransform.parent.rotation;


        if (!networkGrabbable.Object.HasInputAuthority)
            return;
        // Detect movement and apply vibration
        float movementAmount = Mathf.Abs(localPos.z - initialLocalZ);  // How much the object has moved on Z
        bool hasMoved = movementAmount > 0.01f;  // Small threshold to avoid micro movements
        if (hasMoved)
        {
            // Normalize Z movement between 0 and 1
            float normalizedZ = Mathf.InverseLerp(maxZSlideMovement, minZSlideMovement, localPos.z);

            // Scale vibration intensity based on movement
            float vibrationStrength = Mathf.Lerp(0.2f, 1.0f, normalizedZ); // Adjust range as needed

            ApplyVibration(0.5f,vibrationStrength);
        }
        else
        {
            StopVibration();
        }
    }

    public void DidGrab()
    {
        // Store initial local position & rotation
        initialLocalPosition = transform.localPosition;
        initialLocalRotation = transform.localRotation;
        initialLocalY = transform.localPosition.y;
        initialLocalZ = transform.localPosition.z; // Store original local Z position
    }

    public void DidUngrab()
    {
        // Preserve the current Z position but reset X & Y
        float currentZ = transform.localPosition.z;
        transform.localPosition = new Vector3(initialLocalPosition.x, initialLocalPosition.y, currentZ);

        // Ensure it perfectly matches parent rotation when released
        transform.rotation = transform.parent.rotation;

       
    }

    // Vibrate controller dynamically based on strength
    private void ApplyVibration(float frequency, float intensity)
    {
        
        if (currentGrabber)
        {
           
          
            currentGrabber.hand.ApplyVibration(frequency, intensity);

        }
    }

     private void StopVibration()
    {
        if (!networkGrabbable.Object.HasInputAuthority)
            return;
        if (currentGrabber)
            currentGrabber.hand.StopVibration();
    }
}
